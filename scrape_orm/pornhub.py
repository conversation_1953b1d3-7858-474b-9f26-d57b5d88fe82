from peewee import *

# db = SqliteDatabase('pornhub_scrape.db')
db = PostgresqlDatabase(
    "porm",
    schema="pornhub_scraper",
    user="postgres",
    password="postgres",
    host="************",
    port=5432,
)


class BaseModel(Model):
    class Meta:
        database = db


class Performer(BaseModel):
    url = CharField(primary_key=True)
    name = CharField()
    processed = BooleanField(default=False)
    entries = IntegerField(default=1)
    stashdb_id = UUIDField(null=True)
    theporndb_id = UUIDField(null=True)
    fansdb_id = UUIDField(null=True)


class Tag(BaseModel):
    name = CharField()
    url = CharField(primary_key=True)
    entries = IntegerField(default=1)
    stashdb_id = UUIDField(null=True)
    theporndb_id = UUIDField(null=True)
    fansdb_id = UUIDField(null=True)


class Category(BaseModel):
    name = Char<PERSON>ield()
    url = Char<PERSON>ield(primary_key=True)
    entries = IntegerField(default=1)
    stashdb_id = UUIDField(null=True)
    theporndb_id = UUIDField(null=True)
    fansdb_id = UUIDField(null=True)

class Channel(BaseModel):
    title = CharField()
    url = CharField(primary_key=True)
    thumb_url = CharField(primary_key=True)
    entries = IntegerField(default=1)
    rank =  IntegerField(default=1)
    subscribers =  IntegerField(default=1)
    stashdb_id = UUIDField(null=True)
    theporndb_id = UUIDField(null=True)
    fansdb_id = UUIDField(null=True)




class Scene(BaseModel):
    url = CharField(primary_key=True)
    title = CharField()
    duration = IntegerField(default=0)
    processed = BooleanField(default=False)
    performers = ManyToManyField(Performer, backref="scenes")
    views = IntegerField(default=0)
    likes = IntegerField(default=0)
    tags = ManyToManyField(Tag, backref="scenes")
    categories = ManyToManyField(Category, backref="scenes")
    channel = ForeignKeyField(Channel, backref="scenes", null=True)
    stashdb_id = UUIDField(null=True)
    theporndb_id = UUIDField(null=True)
    fansdb_id = UUIDField(null=True)


db.create_tables([
    Performer,
    Tag,
    Category,
    Scene,
])