
import requests
from concurrent.futures import Thread<PERSON><PERSON><PERSON>xecutor, as_completed
from pyquery import PyQuery as pq
import json
from rich.console import Console
from rich.progress import Progress, TaskID, SpinnerColumn, TextColumn, BarColumn, MofNCompleteColumn, TimeElapsedColumn
from rich.live import Live
from rich.table import Table
from rich.panel import Panel
import time
import threading
from typing import List, Dict, Any
from dataclasses import dataclass
from urllib.parse import urlparse

from bookmarks_util import BookmarksUtil


@dataclass
class ScrapingResult:
    url: str
    success: bool
    data: Dict[Any, Any] = None
    error: str = None
    duration: float = 0

class ThreadPoolScraper:
    def __init__(self, max_workers: int = 20, timeout: int = 30):
        self.max_workers = max_workers
        self.timeout = timeout
        self.console = Console()
        self.active_tasks = {}
        self.results = []
        self.lock = threading.Lock()

        # Setup session with connection pooling
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (compatible; DataScraper/1.0)'
        })

    def process_url(self, url: str) -> ScrapingResult:
        """Process a single URL - replace this with your scraping logic"""
        start_time = time.time()
        thread_id = threading.current_thread().ident

        try:
            # Show what we're working on
            domain = urlparse(url).netloc
            with self.lock:
                self.active_tasks[thread_id] = f"Scraping {domain}"

            response = self.session.get(url, timeout=self.timeout)

            if response.status_code == 200:
                # Your JSON-LD extraction logic here
                doc = pq(response.content)
                script_content = doc('script[type="application/ld+json"]').text()

                if script_content:
                    json_data = json.loads(script_content)

                    # Extract the data you need
                    extracted_data = {
                        'name': json_data.get('name'),
                        'duration': json_data.get('duration'),
                        'views': 0,
                        'likes': 0
                    }

                    # Extract interaction stats
                    interactions = json_data.get('interactionStatistic', [])
                    for interaction in interactions:
                        interaction_type = interaction.get('interactionType', '')
                        count_str = interaction.get('userInteractionCount', '0')
                        count = int(count_str.replace(',', ''))

                        if 'WatchAction' in interaction_type:
                            extracted_data['views'] = count
                        elif 'LikeAction' in interaction_type:
                            extracted_data['likes'] = count

                    duration = time.time() - start_time
                    return ScrapingResult(url=url, success=True, data=extracted_data, duration=duration)
                else:
                    duration = time.time() - start_time
                    return ScrapingResult(url=url, success=False, error="No JSON-LD found", duration=duration)
            else:
                duration = time.time() - start_time
                return ScrapingResult(url=url, success=False, error=f"HTTP {response.status_code}", duration=duration)

        except requests.exceptions.Timeout:
            duration = time.time() - start_time
            return ScrapingResult(url=url, success=False, error="Timeout", duration=duration)
        except Exception as e:
            duration = time.time() - start_time
            return ScrapingResult(url=url, success=False, error=str(e), duration=duration)
        finally:
            # Remove from active tasks
            with self.lock:
                self.active_tasks.pop(thread_id, None)

    def create_display(self, progress: Progress) -> Table:
        """Create the display table showing progress and active tasks"""
        table = Table.grid(expand=True)
        table.add_column()

        # Add progress bars
        table.add_row(progress)

        # Add active tasks section
        with self.lock:
            active_tasks_copy = dict(self.active_tasks)

        if active_tasks_copy:
            active_table = Table(title="🔄 Active Tasks", show_header=False, box=None)
            active_table.add_column("Task", style="cyan")

            for thread_id, description in list(active_tasks_copy.items())[:10]:  # Show max 10 active tasks
                active_table.add_row(f"• {description}")

            if len(active_tasks_copy) > 10:
                active_table.add_row(f"... and {len(active_tasks_copy) - 10} more")

            table.add_row("")
            table.add_row(active_table)

        # Add stats
        successful = sum(1 for r in self.results if r.success)
        failed = sum(1 for r in self.results if not r.success)

        if self.results:
            avg_duration = sum(r.duration for r in self.results) / len(self.results)
            stats_text = f"✅ Success: {successful} | ❌ Failed: {failed} | ⏱️  Avg: {avg_duration:.2f}s"
            table.add_row("")
            table.add_row(Panel(stats_text, title="📊 Stats"))

        return table

    def scrape_urls(self, urls: List[str]) -> List[ScrapingResult]:
        """Main scraping function with progress tracking"""

        with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                MofNCompleteColumn(),
                TextColumn("•"),
                TimeElapsedColumn(),
                console=self.console,
                transient=False
        ) as progress:

            main_task = progress.add_task("🌐 Scraping URLs", total=len(urls))

            with Live(self.create_display(progress), refresh_per_second=2, console=self.console):

                with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                    # Submit all tasks
                    future_to_url = {executor.submit(self.process_url, url): url for url in urls}

                    # Process completed tasks
                    for future in as_completed(future_to_url):
                        result = future.result()
                        self.results.append(result)
                        progress.advance(main_task)

        return self.results

# Usage example
def main(url_list):
    # Your list of URLs
    # urls = [
    #            "https://example.com/video1",
    #            "https://example.com/video2",
    #            # ... your 30,000 URLs
    #        ] * 100  # Simulate many URLs for testing

    scraper = ThreadPoolScraper(max_workers=20, timeout=30)

    print("🚀 Starting scraping process...")
    start_time = time.time()

    results = scraper.scrape_urls(url_list)

    end_time = time.time()

    # Summary
    successful = [r for r in results if r.success]
    failed = [r for r in results if not r.success]

    console = Console()
    console.print("\n" + "="*50)
    console.print(f"🎉 [bold green]Scraping Complete![/bold green]")
    console.print(f"⏱️  Total time: {end_time - start_time:.2f} seconds")
    console.print(f"✅ Successful: {len(successful)}")
    console.print(f"❌ Failed: {len(failed)}")

    if successful:
        avg_duration = sum(r.duration for r in successful) / len(successful)
        console.print(f"📊 Average request time: {avg_duration:.2f}s")

    # Show some sample data
    if successful:
        console.print("\n📋 [bold]Sample Results:[/bold]")
        for result in successful[:3]:
            if result.data:
                console.print(f"• {result.data.get('name', 'N/A')} - Views: {result.data.get('views', 0)}")




if __name__ == "__main__":
    bookmarks_util = BookmarksUtil()
    bookmarks = bookmarks_util.load_bookmarks([r"C:\Users\<USER>\AppData\Local\BraveSoftware\Brave-Browser\User Data\Default\Bookmarks",
                                               r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data\Default\Bookmarks"])

    main()