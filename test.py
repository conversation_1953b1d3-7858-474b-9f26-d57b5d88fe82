import xxxparser.pornhub as ph
import xxxparser.pornhub_channel as phc
from pprint import pprint
# import scrape_orm.pornhub as pornhub_orm

if __name__ == "__main__":
    vid_id = "67d97daf5daa0"
    sesh = ph.login()
    # vid_info = ph.get_video_info(sesh, f"https://www.pornhub.com/view_video.php?viewkey={vid_id}")

    vid_info = ph.parse_pornhub_url(ses=sesh, url=f"https://www.pornhub.com/view_video.php?viewkey=682d66190fa47", domain="pornhub.com")
    # channel_info = phc.get_channel_info(sesh, "https://www.pornhub.com/channels/erotique-tv-live")

    pprint(vid_info)

    # pprint(ph.get_video_info(sesh, f"https://www.pornhub.com/view_video.php?viewkey={vid_id}"))
