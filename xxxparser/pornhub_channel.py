import json
import re
import time
import requests
from requests.exceptions import ConnectionError, RequestException
from pyquery import PyQuery as pq
from urllib.parse import quote, parse_qs
from typing import List, Dict, Any, Optional
import logging
from urllib.parse import urlparse
from urllib.parse import urlunparse

from xxxparser import tools
from xxxparser.pornhub import strip_url_params_urllib, load_ph_page


def get_channel_info(
        ses: requests.Session,
        url: str,
        tries: int = 3,
        timeout: int = 5,
        scrapper_key: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Extract video information from a video page.

    Args:
        ses: Session object
        url: Video URL
        tries: Maximum number of retries
        timeout: Timeout between retries
        scrapper_key: API key for webscrapper service

    Returns:
        Dictionary with video information
    """
    # Add Twitter referrer to bypass some restrictions
    url = strip_url_params_urllib(url)

    most_viewed_url =url + "/videos?o=vi&utm_source=twitter"
    top_rated_url =url + "/videos?o=ra&utm_source=twitter"
    main_url = url + "?utm_source=twitter"
    data = load_ph_page(main_url, ses, tries, scrapper_key, timeout)
    mv_data = load_ph_page(most_viewed_url, ses, tries, scrapper_key, timeout)
    tr_data = load_ph_page(top_rated_url, ses, tries, scrapper_key, timeout)

    if not data:
        return {
            "page": url,
            "title": None,
            "duration": None,
            "categories": [],
            "tags": [],
            "pornstars": [],
            "video_links": None,
            "likes": None,
            "views": None,
            "upload_date": None,
            "thumb_url": None,
            "error": "Failed to load page",
        }

    parsed = pq(data)
    parsed_mv = pq(mv_data)
    parsed_tr = pq(tr_data)




    # Find the script tag containing token variable
    script_content = parsed('script:contains("token")').text()

    # Use regex to extract the token value
    token_pattern = r'token\s*=\s*"([^"]+)"'
    token_match = re.search(token_pattern, script_content)

    if token_match:
        token = token_match.group(1)
    else:
        token = None


    channel_id = parsed("div.subscribeButton").attr("data-button-id").replace("subscribe_", "")
    channel_playlist_url = f"https://www.pornhub.com/api/v1/playlist/videos_fake?pid=sys%3Achannel%3A{channel_id}%3Ada%3A0&token={token}"
    channel_playlist_data = load_ph_page(channel_playlist_url, ses, tries, scrapper_key, timeout)
    playlist_vids = json.loads(channel_playlist_data)


    playlist_videos = []
    for vid in playlist_vids["videos"]:
        vkey = vid["vkey"]
        url = f"https://www.pornhub.com/view_video.php?viewkey={vkey}"
        duration = vid["duration"]
        title = tools.clean_multiline_string(vid["title"])
        thumb = vid["xSmallThumbNew"]

        playlist_videos.append({
            "viewkey": vkey,
            "title": title,
            "url": url,
            "duration": duration,
            "thumb": thumb,
        })



    title = tools.clean_multiline_string(parsed("div.title:first").text()) or None
    thumb_url = parsed("img#getAvatar").attr("src") or None
    description = tools.clean_multiline_string(parsed('.cdescriptions p.joined').eq(0).text().strip())

    joined_text = parsed('.cdescriptions p.joined:contains("JOINED") span:last').text()
    channel_name = parsed('.cdescriptions p.joined:contains("WEBSITE") a').text()
    channel_url = parsed('.cdescriptions p.joined:contains("WEBSITE") a').attr('href')

    by_name = parsed('.cdescriptions p.joined:contains("BY") a').text()
    by_url = "https://www.pornhub.com"+parsed('.cdescriptions p.joined:contains("BY") a').attr('href')


    stats = {}
    for div in parsed('#stats .info'):
        element = pq(div)
        value = element.contents()[0].strip()  # Gets text before <br>
        label = element('span').text()  # Gets text inside span
        stats[label] = value

    rank=stats.get("RANK", None)
    subscribers=stats.get("SUBSCRIBERS", None)
    video_views=stats.get("VIDEO VIEWS", None)
    video_count=stats.get("VIDEOS", None)


    def get_videos(p):
        videos = []
        for link in p('li.videoblock'):
            element = pq(link)
            videos.append({
                'url': "https://www.pornhub.com"+element.find('a').attr('href'),
                'title': tools.clean_multiline_string(element.find('a').attr('title')),
                'thumb': element.find('img').attr('src'),
                'duration':tools.convert_duration_string(element.find("var.duration").text()),
            })
        return videos


    # vids = get_videos(parsed)

    most_viewed_vids = get_videos(parsed_mv)
    trending_vids = get_videos(parsed_tr)

    return_dict = {
        "title": title,
        "description": description,
        "thumb_url": thumb_url,
        "joined_text": joined_text,
        "channel_name": channel_name,
        "channel_url": channel_url,
        "by_name": by_name,
        "by_url": by_url,
        "rank": rank,
        "subscribers": subscribers,
        "video_views": video_views,
        "video_count": video_count,
        "most_viewed_vids": most_viewed_vids,
        "trending_vids": trending_vids,
        "playlist_videos": playlist_videos
    }


    return return_dict
