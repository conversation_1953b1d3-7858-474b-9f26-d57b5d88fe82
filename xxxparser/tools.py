import re
from datetime import <PERSON><PERSON><PERSON>

def iso8601_duration_to_seconds(duration_str):
    """
    Convert an ISO 8601 duration string (like PT00H15M31S) to seconds.

    Args:
        duration_str (str): ISO 8601 duration string

    Returns:
        int: Total duration in seconds
    """
    # Method 1: Using regular expressions
    hours = 0
    minutes = 0
    seconds = 0

    # Extract hours
    hour_match = re.search(r'(\d+)H', duration_str)
    if hour_match:
        hours = int(hour_match.group(1))

    # Extract minutes
    minute_match = re.search(r'(\d+)M', duration_str)
    if minute_match:
        minutes = int(minute_match.group(1))

    # Extract seconds
    second_match = re.search(r'(\d+)S', duration_str)
    if second_match:
        seconds = int(second_match.group(1))

    # Calculate total seconds
    total_seconds = hours * 3600 + minutes * 60 + seconds
    return total_seconds


def clean_multiline_string(input_string):
    """
    Remove newlines from a multi-line string and normalize whitespace.

    Args:
        input_string (str): The multi-line string to clean

    Returns:
        str: Cleaned string with normalized whitespace
    """
    # Method 1: Using replace() to replace newlines with spaces
    result1 = input_string.replace('\n', ' ').replace('\r', ' ')

    # Method 2: Using regular expressions (more powerful)

    result2 = re.sub(r'\s+', ' ', input_string).strip()

    # Method 3: Split and join (similar to your fix_text function)
    result3 = ' '.join([line.strip() for line in input_string.splitlines() if line.strip()])

    # Choose the method that best fits your needs
    return result2  # Using regex approach as default

def fix_text(test_str: str) -> str:
    return ' '.join([line.strip() for line in test_str.strip().splitlines() if line.strip()])

def clean_string(input_string):
    # Replace newlines with spaces
    result = re.sub(r'\s+', ' ', input_string).strip()
    result = re.sub(r'\s+', ' ', input_string).strip()
    # result = input_string.replace('\n', ' ')
    #
    # # Strip leading and trailing whitespace
    # result = result.strip()
    #
    # # Replace multiple spaces with a single space
    # result = re.sub(r'\s+', ' ', result)

    return result

def convert_duration_string(duration_str):
    """
    Convert a duration string (MM:SS or HH:MM:SS) to seconds.

    Args:
        duration_str (str): Duration string in format "MM:SS" or "HH:MM:SS"

    Returns:
        int: Total duration in seconds
    """
    # Clean the input string
    duration_str = duration_str.strip()

    # Split by colon
    parts = duration_str.split(':')

    if len(parts) == 2:  # MM:SS format
        minutes, seconds = map(int, parts)
        total_seconds = minutes * 60 + seconds
    elif len(parts) == 3:  # HH:MM:SS format
        hours, minutes, seconds = map(int, parts)
        total_seconds = hours * 3600 + minutes * 60 + seconds
    else:
        raise ValueError(f"Unexpected duration format: {duration_str}")

    return total_seconds

def convert_to_int(value_str):
    """
    Convert strings with K/M suffixes to integers.

    Examples:
        '1.3M' -> 1300000
        '12K' -> 12000
        '23414' -> 23414
    """
    if not isinstance(value_str, str):
        return int(value_str)

    value_str = value_str.strip().upper()

    if value_str.endswith('K'):
        return int(float(value_str[:-1]) * 1_000)
    elif value_str.endswith('M'):
        return int(float(value_str[:-1]) * 1_000_000)
    else:
        return int(float(value_str))
