import json
import re
import time
import requests
from requests.exceptions import ConnectionError, RequestException
from pyquery import PyQuery as pq
from urllib.parse import quote, parse_qs
from typing import List, Dict, Any, Optional
import logging
from urllib.parse import urlparse
from urllib.parse import urlunparse


# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Try to import webscrapper module if available
try:
    from webscrapper_client_api import WebscrapperClientAPI, WebscrapperAPIError

    WEBSCRAPPER_AVAILABLE = True
except ModuleNotFoundError:
    WEBSCRAPPER_AVAILABLE = False
    logger.warning(
        "Webscrapper client module not found. You can install it with: pip install webscrapper-client-api"
    )


def login() -> requests.Session:
    """
    Create and return a session with proper headers for the website.

    Returns:
        requests.Session: Configured session object
    """
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; rv:103.0) Gecko/20100101 Firefox/103.0",
        "Referer": "https://www.pornhub.com/",
        "Accept-encoding": "gzip, deflate",
    }
    ses = requests.Session()
    ses.cookies.clear()
    ses.headers.update(headers)

    # Set age verification cookies
    ses.cookies.set("age_verified", "1")
    ses.cookies.set("accessAgeDisclaimerPH", "1")

    return ses


def load_ph_page(
    url: str,
    ses: requests.Session,
    max_tries: int,
    scrapper_key: Optional[str] = None,
    timeout: int = 5,
) -> Optional[str]:
    """
    Load a page using webscrapper API or internal session with retries.

    Args:
        url: URL to load
        ses: Session object
        max_tries: Maximum number of retries
        scrapper_key: API key for webscrapper service
        timeout: Timeout between retries (in seconds)

    Returns:
        HTML content or None if all retries failed
    """
    retries = 0
    data = None

    while data is None and retries <= max_tries:
        try:
            if scrapper_key and WEBSCRAPPER_AVAILABLE:
                cookies = {
                    "age_verified": "1",
                    "accessAgeDisclaimerPH": "1",
                    "accessAgeDisclaimerUK": "1",
                    "accessPH": "1",
                }

                logger.info("Using Scrapper API")
                with WebscrapperClientAPI(scrapper_key) as client:

                    result = client.get_page(url=url, cookies=cookies)

                if result.get("error"):
                    data = None
                    logger.error(f"Scrapper API error: {result.get('error')}")
                else:
                    data = result.get("html")
            else:
                r = ses.get(url, timeout=timeout)
                if r.status_code == 200:
                    data = r.text
                else:
                    logger.error(f"HTTP error: {r.status_code}")
                    data = None
        except ConnectionError:
            retries += 1
            sleep_time = timeout * retries
            logger.warning(f"Connection error, sleeping for {sleep_time} seconds")
            time.sleep(sleep_time)
        except (RequestException, WebscrapperAPIError) as e:
            logger.error(f"Error loading page: {e}")

            retries += 1
            sleep_time = timeout * retries
            logger.error(f"Request error: {e}, sleeping for {sleep_time} seconds")
            time.sleep(sleep_time)

        if data is None and retries <= max_tries:
            retries += 1

    if data is None:
        logger.error(f"Failed to load page after {max_tries} attempts: {url}")

    return data

def fix_text(test_str: str) -> str:
    return ' '.join([line.strip() for line in test_str.strip().splitlines() if line.strip()])

def clean_string(input_string):
    # Replace newlines with spaces
    result = re.sub(r'\s+', ' ', input_string).strip()
    result = re.sub(r'\s+', ' ', input_string).strip()
    # result = input_string.replace('\n', ' ')
    #
    # # Strip leading and trailing whitespace
    # result = result.strip()
    #
    # # Replace multiple spaces with a single space
    # result = re.sub(r'\s+', ' ', result)

    return result


def clean_multiline_string(input_string):
    """
    Remove newlines from a multi-line string and normalize whitespace.

    Args:
        input_string (str): The multi-line string to clean

    Returns:
        str: Cleaned string with normalized whitespace
    """
    # Method 1: Using replace() to replace newlines with spaces
    result1 = input_string.replace('\n', ' ').replace('\r', ' ')

    # Method 2: Using regular expressions (more powerful)
    import re
    result2 = re.sub(r'\s+', ' ', input_string).strip()

    # Method 3: Split and join (similar to your fix_text function)
    result3 = ' '.join([line.strip() for line in input_string.splitlines() if line.strip()])

    # Choose the method that best fits your needs
    return result2  # Using regex approach as default


def convert_to_int(value_str):
    """
    Convert strings with K/M suffixes to integers.

    Examples:
        '1.3M' -> 1300000
        '12K' -> 12000
        '23414' -> 23414
    """
    if not isinstance(value_str, str):
        return int(value_str)

    value_str = value_str.strip().upper()

    if value_str.endswith('K'):
        return int(float(value_str[:-1]) * 1_000)
    elif value_str.endswith('M'):
        return int(float(value_str[:-1]) * 1_000_000)
    else:
        return int(float(value_str))


def get_video_info(
    ses: requests.Session,
    url: str,
    tries: int = 3,
    timeout: int = 5,
    scrapper_key: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Extract video information from a video page.

    Args:
        ses: Session object
        url: Video URL
        tries: Maximum number of retries
        timeout: Timeout between retries
        scrapper_key: API key for webscrapper service

    Returns:
        Dictionary with video information
    """
    # Add Twitter referrer to bypass some restrictions
    if "?" in url:
        url += "&utm_source=twitter"
    else:
        url += "?utm_source=twitter"

    data = load_ph_page(url, ses, tries, scrapper_key, timeout)

    if not data:
        return {
            "page": url,
            "title": None,
            "duration": None,
            "categories": [],
            "tags": [],
            "pornstars": [],
            "video_links": None,
            "likes": None,
            "views": None,
            "upload_date": None,
            "thumb_url": None,
            "error": "Failed to load page",
        }

    parsed = pq(data)


    title = parsed("h1.title:first").text() or None
    json_data = json.loads(parsed("script[type='application/ld+json']").text())
    duration = json_data.get('duration')
    upload_date = json_data.get('uploadDate')
    thumb_url = json_data.get('thumbnailUrl')
    # views = convert_to_int(parsed("div.views .count").text() or 0)
    # likes = convert_to_int(parsed("span.votesUp").text() or 0)

    views = 0
    likes = 0
    for interaction in json_data['interactionStatistic']:
        interaction_type = interaction.get('interactionType', '')
        count_str = interaction.get('userInteractionCount', '0')

        # Convert comma-separated string to integer
        count = int(count_str.replace(',', ''))

        if interaction_type == 'http://schema.org/WatchAction':
            views = count
        elif interaction_type == 'http://schema.org/LikeAction':
            likes = count

    categories = [{"category": clean_string(c.text()), "link":c.attr('href') } for c in parsed(".categoriesWrapper a").items()]
    pornstars =[{"pornstar": clean_string(c.text()), "link":c.attr('href') } for c in parsed(".pornstarsWrapper a.pstar-list-btn").items()]
    # pornstars = [c.text() for c in parsed(".pornstarsWrapper a.pstar-list-btn").items()]
    tags = [{"tag": clean_string(c.text()), "link":c.attr('href') } for c in parsed(".tagsWrapper a").items()]

    video_links=[]
    for c in parsed(".videoblock").items():
        video_links.append({
            "title":clean_string(c.find("a.linkVideoThumb").attr('title')),
            "link":c.find("a.linkVideoThumb").attr("href"),
            "duration":c.find("a.linkVideoThumb").find(".duration").text(),
            "uploader":clean_string(c.find(".videoUploaderBlock a").text()),
            "uploader_link":c.find(".videoUploaderBlock a").attr("href"),
            "views":c.find(".videoUploaderBlock span.views").text() ,
            "rating":c.find(".videoUploaderBlock div.rating-container").text()
        })

    # title = c.find("a.linkVideoThumb").attr('title')
    # video_links = [{
    #     "title":clean_string(c.find("a.linkVideoThumb").attr('title')),
    #     "link":c.find("a.linkVideoThumb").attr("href"),
    #     "duration":c.find("a.linkVideoThumb").find(".duration").text(),
    #     "uploader":clean_string(c.find(".videoUploaderBlock a").text()),
    #     "uploader_link":c.find(".videoUploaderBlock a").attr("href"),
    #     "views":c.find(".videoUploaderBlock span.views").text() ,
    #     "rating":c.find(".videoUploaderBlock div.rating-container").text()
    # } for c in parsed(".videoblock").items()]

    # comments = [c.text() for c in parsed(".commentMessage span").items()]
    return {
        "page": url,
        "title": title,
        "categories": categories,
        "tags": tags,
        "pornstars": pornstars,
        "video_links": video_links,
        "likes": likes,
        "views": views,
        "duration": duration,
        "upload_date": upload_date,
        "thumb_url": thumb_url,
        # "comments": comments,
        "error": None,
    }

def strip_url_params_urllib(url):
    """
    Uses urllib.parse to remove all query parameters
    """
    parsed = urlparse(url)
    clean_url = urlunparse((parsed.scheme, parsed.netloc, parsed.path, '', '', ''))
    return clean_url


def get_channel_info(
        ses: requests.Session,
        url: str,
        tries: int = 3,
        timeout: int = 5,
        scrapper_key: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Extract video information from a video page.

    Args:
        ses: Session object
        url: Video URL
        tries: Maximum number of retries
        timeout: Timeout between retries
        scrapper_key: API key for webscrapper service

    Returns:
        Dictionary with video information
    """
    # Add Twitter referrer to bypass some restrictions
    url = strip_url_params_urllib(url)

    most_viewed_url =url + "/videos?o=vi&utm_source=twitter"
    top_rated_url =url + "/videos?o=ra&utm_source=twitter"
    main_url = url + "?utm_source=twitter"
    data = load_ph_page(main_url, ses, tries, scrapper_key, timeout)
    mv_data = load_ph_page(most_viewed_url, ses, tries, scrapper_key, timeout)
    tr_data = load_ph_page(top_rated_url, ses, tries, scrapper_key, timeout)

    if not data:
        return {
            "page": url,
            "title": None,
            "duration": None,
            "categories": [],
            "tags": [],
            "pornstars": [],
            "video_links": None,
            "likes": None,
            "views": None,
            "upload_date": None,
            "thumb_url": None,
            "error": "Failed to load page",
        }

    parsed = pq(data)
    parsed_mv = pq(mv_data)
    parsed_tr = pq(tr_data)


    playlist_link = "https://www.pornhub.com"+parsed("a.playAllBtnNew").attr("href")

    script_content = parsed('script:contains("WIDGET_RATINGS_LIKE_FAV")').text()

    # Find the script tag containing token variable
    script_content = parsed('script:contains("token")').text()

    # Use regex to extract the token value
    token_pattern = r'token\s*=\s*"([^"]+)"'
    token_match = re.search(token_pattern, script_content)

    if token_match:
        token = token_match.group(1)
        print(f"Token: {token}")
    else:
        token = None
        print("Token not found")

    channel_id = parsed("div.subscribeButton").attr("data-button-id").replace("subscribe_", "")
    channel_playlist_url = f"https://www.pornhub.com/api/v1/playlist/videos_fake?pid=sys%3Achannel%3A{channel_id}%3Ada%3A0&token={token}"
    channel_playlist_data = load_ph_page(channel_playlist_url, ses, tries, scrapper_key, timeout)
    playlist_data=load_ph_page(playlist_link, ses, tries, scrapper_key, timeout)
    playlist_vids = json.loads(channel_playlist_data)


    playlist_videos = []
    for vid in playlist_vids["videos"]:
        print(vid["title"])
        vkey = vid["vkey"]
        url = f"https://www.pornhub.com/view_video.php?viewkey={vkey}"
        duration = vid["duration"]
        duration_formatted = vid["durationFormatted"]
        title = vid["title"]
        thumb = vid["xSmallThumbNew"]

        playlist_videos.append({
            "viewkey": vkey,
            "title": title,
            "url": url,
            "duration": duration,
            "duration_formatted": duration_formatted,
            "thumb": thumb,
        })



    title = parsed("div.title:first").text() or None
    description = parsed("div.cdescriptions:first").text() or None
    thumb_url = parsed("img#getAvatar").attr("src") or None
    # descriptions = {}
    description = parsed('.cdescriptions p.joined').eq(0).text().strip()
    joined_text = parsed('.cdescriptions p.joined:contains("JOINED") span:last').text()
    # website_url = parsed('.cdescriptions p.joined a:contains("ZeroToleranceFilms")').attr('href')
    # website_name = parsed('.cdescriptions p.joined a:contains("ZeroToleranceFilms")').text()
    channel_name = parsed('.cdescriptions p.joined:contains("WEBSITE") a').text()
    channel_url = parsed('.cdescriptions p.joined:contains("WEBSITE") a').attr('href')

    by_name = parsed('.cdescriptions p.joined:contains("BY") a').text()
    by_url = parsed('.cdescriptions p.joined:contains("BY") a').attr('href')

    # for div in parsed('.cdescriptions p'):
    #     pass


    stats = {}
    for div in parsed('#stats .info'):
        element = pq(div)
        # Get the text before <br> for value and the span text for label
        value = element.contents()[0].strip()  # Gets text before <br>
        label = element('span').text()  # Gets text inside span
        stats[label] = value

    rank=stats.get("RANK", None)
    subscribers=stats.get("SUBSCRIBERS", None)
    video_views=stats.get("VIDEO VIEWS", None)
    video_count=stats.get("VIDEOS", None)


    def get_videos(p):
        videos = []
        for link in p('li.videoblock'):
            element = pq(link)
            videos.append({
                'url': "https://www.pornhub.com"+element.find('a').attr('href'),
                'title': element.find('a').attr('title'),
                'thumb': element.find('img').attr('src'),
                'duration':element.find("var.duration").text(),
            })
        return videos


    # vids = get_videos(parsed)

    most_viewed_vids = get_videos(parsed_mv)
    trending_vids = get_videos(parsed_tr)

    return_dict = {
        "title": title,
        "description": description,
        "thumb_url": thumb_url,
        "joined_text": joined_text,
        "channel_name": channel_name,
        "channel_url": channel_url,
        "by_name": by_name,
        "by_url": by_url,
        "rank": rank,
        "subscribers": subscribers,
        "video_views": video_views,
        "video_count": video_count,
        "most_viewed_vids": most_viewed_vids,
        "trending_vids": trending_vids,
        "playlist_videos": playlist_videos
    }


    return return_dict

    # video_links=[]
    # for c in parsed(".videoblock").items():
    #     video_links.append({
    #         "title":clean_string(c.find("a.linkVideoThumb").attr('title')),
    #         "link":c.find("a.linkVideoThumb").attr("href"),
    #         "duration":c.find("a.linkVideoThumb").find(".duration").text(),
    #         "uploader":clean_string(c.find(".videoUploaderBlock a").text()),
    #         "uploader_link":c.find(".videoUploaderBlock a").attr("href"),
    #         "views":c.find(".videoUploaderBlock span.views").text() ,
    #         "rating":c.find(".videoUploaderBlock div.rating-container").text()
    #     })
    #

    # json_data = json.loads(parsed("script[type='application/ld+json']").text())
    # duration = json_data.get('duration')
    # upload_date = json_data.get('uploadDate')
    # thumb_url = json_data.get('thumbnailUrl')


    # views = convert_to_int(parsed("div.views .count").text() or 0)
    # likes = convert_to_int(parsed("span.votesUp").text() or 0)

    # views = 0
    # likes = 0
    # for interaction in json_data['interactionStatistic']:
    #     interaction_type = interaction.get('interactionType', '')
    #     count_str = interaction.get('userInteractionCount', '0')
    #
    #     # Convert comma-separated string to integer
    #     count = int(count_str.replace(',', ''))
    #
    #     if interaction_type == 'http://schema.org/WatchAction':
    #         views = count
    #     elif interaction_type == 'http://schema.org/LikeAction':
    #         likes = count

    # categories = [{"category": clean_string(c.text()), "link":c.attr('href') } for c in parsed(".categoriesWrapper a").items()]
    # pornstars =[{"pornstar": clean_string(c.text()), "link":c.attr('href') } for c in parsed(".pornstarsWrapper a.pstar-list-btn").items()]
    # # pornstars = [c.text() for c in parsed(".pornstarsWrapper a.pstar-list-btn").items()]
    # tags = [{"tag": clean_string(c.text()), "link":c.attr('href') } for c in parsed(".tagsWrapper a").items()]
    #


    # title = c.find("a.linkVideoThumb").attr('title')
    # video_links = [{
    #     "title":clean_string(c.find("a.linkVideoThumb").attr('title')),
    #     "link":c.find("a.linkVideoThumb").attr("href"),
    #     "duration":c.find("a.linkVideoThumb").find(".duration").text(),
    #     "uploader":clean_string(c.find(".videoUploaderBlock a").text()),
    #     "uploader_link":c.find(".videoUploaderBlock a").attr("href"),
    #     "views":c.find(".videoUploaderBlock span.views").text() ,
    #     "rating":c.find(".videoUploaderBlock div.rating-container").text()
    # } for c in parsed(".videoblock").items()]

    # comments = [c.text() for c in parsed(".commentMessage span").items()]
    # return {
    #     "page": url,
    #     "title": title,
    #     "categories": categories,
    #     "tags": tags,
    #     "pornstars": pornstars,
    #     "video_links": video_links,
    #     "likes": likes,
    #     "views": views,
    #     "duration": duration,
    #     "upload_date": upload_date,
    #     "thumb_url": thumb_url,
    #     # "comments": comments,
    #     "error": None,
    # }


def parse_pornhub_url(
    ses: requests.Session,
    url: str,
    domain: str,
    DEBUG: bool = False,
    tries: int = 3,
    timeout: int = 5,
    scrapper_key: Optional[str] = None,
) -> List[str]:
    """
    Parse a list page and extract video URLs.

    Args:
        ses: Session object
        url: List page URL
        domain: Base domain
        DEBUG: Enable debug output
        tries: Maximum number of retries
        timeout: Timeout between retries
        scrapper_key: API key for webscrapper service

    Returns:
        List of video URLs
    """
    result = []
    data = load_ph_page(url, ses, tries, scrapper_key, timeout)

    if not data:
        logger.error(f"Failed to load page: {url}")
        return result

    parsed = pq(data)
    # items = parsed.items("li.videoBox a:first")
    items = parsed.items("li.videoblock")
    i = 0

    for el in items:
        i += 1
        video_url = el.attr("href")
        video_title = el.attr("title")
        if video_url and "view_video" in video_url:
            if DEBUG:
                logger.info(f"New video URL found: {video_url}")

            # Ensure URL is properly formatted
            if video_url.startswith("/"):
                full_url = f"{domain}{video_url}"
            else:
                full_url = f"{domain}/{video_url}"
            parsed = urlparse(url)
            viewkey = parse_qs(parsed.query)["viewkey"][0]
            # video_key = urlparse(full_url).path.split("/")[-1]
            result.append({
                "viewkey": viewkey,
                "title": video_title,
                "url": full_url
            })

    if i == 0:
        logger.error(f"No videos found on page: {url}")
        logger.info(data)
        if DEBUG:
            logger.debug(f"Page content: {data[:1000]}...")

    return result


def search_videos(
    ses: requests.Session,
    query: str,
    pages: List[int] = [1, 2],
    rus: bool = False,
    recent: bool = False,
    DEBUG: bool = False,
    scrapper_key: Optional[str] = None,
    wait_time: int = 10,
) -> List[str]:
    """
    Search for videos matching a query.

    Args:
        ses: Session object
        query: Search query
        pages: List of page numbers to fetch
        rus: Use Russian domain
        recent: Sort by most recent
        DEBUG: Enable debug output
        scrapper_key: API key for webscrapper service
        wait_time: Time to wait between page loads

    Returns:
        List of video URLs
    """
    recent_query = "&o=mr" if recent else ""
    www_domain = "rt" if rus else "www"
    domain = f"https://{www_domain}.pornhub.com"

    b_url = (
        f"https://{www_domain}.pornhub.com/video/search?search="
        f"{quote(query)}{recent_query}&p=homemade&page="
    )
    result = []
    for p in pages:
        url = b_url + str(p)
        # print(url)
        if DEBUG:
            logger.info(f"Loading: {url}")

        new = parse_pornhub_url(ses, url, domain, DEBUG, scrapper_key=scrapper_key)
        if new:
            result.extend(new)

        if p < max(pages) and wait_time > 0:  # Don't wait after the last page
            logger.info(f"Waiting {wait_time} seconds before next request")
            time.sleep(wait_time)  # User behavior emulation
    return result


def get_recent_videos(
    ses: requests.Session,
    pages: List[int] = [2],
    rus: bool = False,
    DEBUG: bool = False,
    scrapper_key: Optional[str] = None,
    wait_time: int = 10,
) -> List[str]:
    """
    Get recent videos from the home page.

    Args:
        ses: Session object
        pages: List of page numbers to fetch
        rus: Use Russian domain
        DEBUG: Enable debug output
        scrapper_key: API key for webscrapper service
        wait_time: Time to wait between page loads

    Returns:
        List of video URLs
    """
    result = []

    if rus:
        domain = "https://rt.pornhub.com"
        b_url = "https://rt.pornhub.com/video?p=homemade&o=mv&t=a&cc=ru&hd=1&page="
    else:
        domain = "https://www.pornhub.com"
        b_url = "https://www.pornhub.com/video?p=homemade&o=mv&cc=ru&page="

    for p in pages:
        url = b_url + str(p)
        if DEBUG:
            logger.info(f"Loading: {url}")

        new = parse_pornhub_url(ses, url, domain, DEBUG, scrapper_key=scrapper_key)
        if new:
            result.extend(new)

        if p < max(pages) and wait_time > 0:  # Don't wait after the last page
            logger.info(f"Waiting {wait_time} seconds before next request")
            time.sleep(wait_time)  # User behavior emulation

    return result


def get_hot_videos(
    ses: requests.Session,
    pages: List[int] = [2],
    hm: bool = True,
    country: Optional[str] = None,
    DEBUG: bool = False,
    mv: bool = False,
    scrapper_key: Optional[str] = None,
    wait_time: int = 10,
) -> List[str]:
    """
    Get hot videos from the home page.

    Args:
        ses: Session object
        pages: List of page numbers to fetch
        hm: Filter for homemade videos
        country: Filter by country code
        DEBUG: Enable debug output
        mv: Sort by most viewed instead of hottest
        scrapper_key: API key for webscrapper service
        wait_time: Time to wait between page loads

    Returns:
        List of video URLs
    """
    result = []
    domain = "https://www.pornhub.com"

    # Build the URL
    if mv:
        b_url = f"{domain}/video?o=mv"  # Most viewed
    else:
        b_url = f"{domain}/video?o=ht"  # Hottest

    if hm:
        b_url = f"{b_url}&p=homemade"

    if country:
        b_url = f"{b_url}&cc={country}"

    b_url = f"{b_url}&page="

    for p in pages:
        url = b_url + str(p)
        if DEBUG:
            logger.info(f"Loading: {url}")

        new = parse_pornhub_url(ses, url, domain, DEBUG, scrapper_key=scrapper_key)
        if new:
            result.extend(new)

        if p < max(pages) and wait_time > 0:  # Don't wait after the last page
            logger.info(f"Waiting {wait_time} seconds before next request")
            time.sleep(wait_time)  # User behavior emulation

    return result


if __name__ == "__main__":
    ses = login()
    # example usage:
    for v in search_videos(ses, "blowjob and anal", DEBUG=True)[:5]:
        print("Video:", v)
        print(get_video_info(ses, v))
        time.sleep(5)

    for v in get_recent_videos(ses, DEBUG=True)[:5]:
        print("Video:", v)
        print(get_video_info(ses, v))
        time.sleep(5)
