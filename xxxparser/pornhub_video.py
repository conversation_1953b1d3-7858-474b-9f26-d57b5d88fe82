import json
import re
import time
import requests
from requests.exceptions import ConnectionError, RequestException
from pyquery import PyQuery as pq
from urllib.parse import quote, parse_qs
from typing import List, Dict, Any, Optional
import logging
from urllib.parse import urlparse
from urllib.parse import urlunparse

from xxxparser import tools
from xxxparser.pornhub import load_ph_page
from xxxparser.tools import clean_string


def get_video_info(
        ses: requests.Session,
        url: str,
        tries: int = 3,
        timeout: int = 5,
        scrapper_key: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Extract video information from a video page.

    Args:
        ses: Session object
        url: Video URL
        tries: Maximum number of retries
        timeout: Timeout between retries
        scrapper_key: API key for webscrapper service

    Returns:
        Dictionary with video information
    """
    # Add Twitter referrer to bypass some restrictions
    if "?" in url:
        url += "&utm_source=twitter"
    else:
        url += "?utm_source=twitter"

    data = load_ph_page(url, ses, tries, scrapper_key, timeout)

    if not data:
        return {
            "page": url,
            "title": None,
            "duration": None,
            "categories": [],
            "tags": [],
            "pornstars": [],
            "video_links": None,
            "likes": None,
            "views": None,
            "upload_date": None,
            "thumb_url": None,
            "error": "Failed to load page",
        }

    parsed = pq(data)


    title = parsed("h1.title:first").text() or None
    json_data = json.loads(parsed("script[type='application/ld+json']").text())
    duration = tools.iso8601_duration_to_seconds(json_data.get('duration'))
    upload_date = json_data.get('uploadDate')
    thumb_url = json_data.get('thumbnailUrl')
    # views = convert_to_int(parsed("div.views .count").text() or 0)
    # likes = convert_to_int(parsed("span.votesUp").text() or 0)

    views = 0
    likes = 0
    for interaction in json_data['interactionStatistic']:
        interaction_type = interaction.get('interactionType', '')
        count_str = interaction.get('userInteractionCount', '0')

        # Convert comma-separated string to integer
        count = int(count_str.replace(',', ''))

        if interaction_type == 'http://schema.org/WatchAction':
            views = count
        elif interaction_type == 'http://schema.org/LikeAction':
            likes = count

    categories = [{"category": clean_string(c.text()), "link": "https://www.pornhub.com"+c.attr('href')} for c in parsed(".categoriesWrapper a").items() if c.attr('href')]
    pornstars = [{"pornstar": clean_string(c.text()), "link": "https://www.pornhub.com"+c.attr('href')} for c in parsed(".pornstarsWrapper a.pstar-list-btn").items() if c.attr('href')]
    tags = []
    for c in parsed(".tagsWrapper a").items():
        href = c.attr('href')
        if href:  # This checks if href is not None and not empty
            tags.append({
                "tag": clean_string(c.text()),
                "link": "https://www.pornhub.com" + href
            })

    video_links = []
    for c in parsed(".videoblock").items():
        link_href = c.find("a.linkVideoThumb").attr("href")
        if link_href:  # Only add if href exists
            video_links.append({
                "title": tools.clean_multiline_string(c.find("a.linkVideoThumb").attr('title') or ""),
                "link": "https://www.pornhub.com" + link_href,
                "duration": tools.convert_duration_string(c.find("a.linkVideoThumb").find(".duration").text() or "0:00"),
                "uploader": tools.clean_multiline_string(c.find(".videoUploaderBlock a").text() or ""),
                "uploader_link": "https://www.pornhub.com" + (c.find(".videoUploaderBlock a").attr("href") or ""),
                "views": c.find(".videoUploaderBlock span.views").text() or "",
                "rating": c.find(".videoUploaderBlock div.rating-container").text() or ""
            })

    # title = c.find("a.linkVideoThumb").attr('title')
    # video_links = [{
    #     "title":clean_string(c.find("a.linkVideoThumb").attr('title')),
    #     "link":c.find("a.linkVideoThumb").attr("href"),
    #     "duration":c.find("a.linkVideoThumb").find(".duration").text(),
    #     "uploader":clean_string(c.find(".videoUploaderBlock a").text()),
    #     "uploader_link":c.find(".videoUploaderBlock a").attr("href"),
    #     "views":c.find(".videoUploaderBlock span.views").text() ,
    #     "rating":c.find(".videoUploaderBlock div.rating-container").text()
    # } for c in parsed(".videoblock").items()]

    # comments = [c.text() for c in parsed(".commentMessage span").items()]
    return {
        "page": url,
        "title": title,
        "categories": categories,
        "tags": tags,
        "pornstars": pornstars,
        "video_links": video_links,
        "likes": likes,
        "views": views,
        "duration": duration,
        "upload_date": upload_date,
        "thumb_url": thumb_url,
        # "comments": comments,
        "error": None,
    }
